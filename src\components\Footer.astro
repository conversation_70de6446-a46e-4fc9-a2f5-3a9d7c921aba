---
// Footer component for Cheers Marketplace - Optimized
const currentYear = new Date().getFullYear();

const footerSections = [
  {
    title: 'Quick Links',
    links: [
      { href: '/', text: 'Home' },
      { href: '/products/', text: 'Products' },
      { href: '/about/', text: 'About' },
      { href: '/faq/', text: 'FAQ' }
    ]
  },
  {
    title: 'Customer Service',
    links: [
      { href: '/faq/', text: 'Help Center' },
      { href: '/condition-guide/', text: 'Condition Guide' },
      { href: 'mailto:<EMAIL>', text: 'Contact Support' },
      { href: '/faq/#shipping', text: 'Shipping Info' },
      { href: '/faq/#returns', text: 'Returns & Exchanges' }
    ]
  },
  {
    title: 'Legal',
    links: [
      { href: '/terms/', text: 'Terms & Conditions' },
      { href: '/privacy/', text: 'Privacy Policy' },
      { href: '/faq/#cookies', text: 'Cookie Policy' }
    ]
  }
];

const socialLinks = [
  { href: '#', label: 'Facebook', icon: 'M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z' },
  { href: '#', label: 'Twitter', icon: 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z' },
  { href: '#', label: 'Instagram', icon: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z' }
];
---

<footer class="site-footer">
  <div class="container">
    <div class="footer-content">
      <!-- Company Info -->
      <div class="footer-brand">
        <a href="/" class="footer-logo-link">
          <img src="/logo.png" alt="Cheers Marketplace Logo" class="footer-logo-image" width="400" height="100" />
        </a>
        <p class="footer-description">
          Discover unique, curated products from passionate creators. We connect you with the best local and global makers, all in one warm, welcoming place.
        </p>
      </div>

      <!-- Dynamic Footer Sections -->
      {footerSections.map(section => (
        <div class="footer-section">
          <h3 class="footer-title">{section.title}</h3>
          <ul class="footer-links">
            {section.links.map(link => (
              <li><a href={link.href}>{link.text}</a></li>
            ))}
          </ul>
        </div>
      ))}
    </div>

    <!-- Footer Bottom -->
    <div class="footer-bottom">
      <div class="footer-bottom-content">
        <p class="copyright">
          © {currentYear} Cheers Marketplace. All rights reserved.
        </p>
        <div class="footer-social">
          <span class="social-text">Follow us:</span>
          {socialLinks.map(social => (
            <a href={social.href} class="social-link" aria-label={social.label}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d={social.icon}/>
              </svg>
            </a>
          ))}
        </div>
      </div>
    </div>
  </div>
</footer>

<style>
  .site-footer {
    background: var(--text);
    color: white;
    margin-top: auto;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr repeat(3, 1fr);
    gap: 3rem;
    padding: 3rem 0 2rem;
  }

  .footer-brand {
    max-width: 300px;
  }

  .footer-logo-link {
    display: inline-block;
    margin-bottom: 1rem;
    transition: opacity 0.2s ease;
  }

  .footer-logo-link:hover {
    opacity: 0.8;
  }

  .footer-logo-image {
    height: 100px;
    width: auto;
    max-width: 400px;
    filter: brightness(0) invert(1); /* Make logo white for dark footer */
    transition: opacity 0.2s ease;
    display: block;
  }

  .footer-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
    font-size: 1rem;
  }
  .footer-description {
    color: #cbd5e1;
    line-height: 1.6;
    margin: 0;
  }

  .footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .footer-links li {
    margin-bottom: 0.5rem;
  }

  .footer-links a {
    color: #cbd5e1;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
  }

  .footer-links a:hover {
    color: white;
  }

  .footer-bottom {
    border-top: 1px solid #374151;
    padding: 1.5rem 0;
  }

  .footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .copyright,
  .social-text {
    color: #9ca3af;
    font-size: 0.875rem;
    margin: 0;
  }

  .footer-social {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .social-link {
    color: #9ca3af;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
  }

  .social-link:hover {
    color: white;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .footer-content {
      grid-template-columns: 1fr;
      gap: 2rem;
      padding: 2rem 0 1.5rem;
    }

    .footer-brand {
      max-width: none;
      text-align: center;
    }

    .footer-logo-image {
      height: 80px;
      max-width: 320px;
    }

    .footer-bottom-content {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }

  @media (max-width: 480px) {
    .footer-content {
      gap: 1.5rem;
      padding: 1.5rem 0 1rem;
    }

    .footer-logo-image {
      height: 70px;
      max-width: 280px;
    }

    .footer-social {
      gap: 0.75rem;
    }
  }
</style>

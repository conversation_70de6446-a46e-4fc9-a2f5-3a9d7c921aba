---
export const prerender = true;

import Layout from '../../layouts/Layout.astro';
import Header from '../../components/Header.astro';
import Footer from '../../components/Footer.astro';
import BreadcrumbWrapper from '../../components/navigation/BreadcrumbWrapper.astro';
import ProductSchema from '../../components/seo/ProductSchema.astro';
import AddToCartButton from '../../components/AddToCartButton.astro';

import { generateSlug, getFeaturedImage, type Product } from '../../lib/products';
import { generateProductDescription } from '../../utils/metaDescriptions.ts';
import '../../assets/global.css';

// Import products data directly for static generation
import products from '../../data/products.json';

export async function getStaticPaths() {
  return (products as Product[]).map((product) => ({
    params: { slug: generateSlug(product.name) },
    props: { product },
  }));
}

const { product }: { product: Product } = Astro.props;

if (!product) {
  return Astro.redirect('/products');
}

// Generate SEO-optimized meta description
const metaDescription = generateProductDescription({
  name: product.name,
  category: product.category,
  price: product.price,
  shortDescription: (product.description || '').substring(0, 100),
  description: product.description,
  condition: 'UsedCondition',
  brand: 'Cheers Marketplace'
});

const pageTitle = `${product.name} | Cheers Marketplace`;
const productSlug = generateSlug(product.name);

// Optimize image URLs for better performance
function optimizeImageUrl(url: string, width: number = 800, height: number = 600): string {
  if (!url) return '/images/product-placeholder.svg';

  if (url.includes('unsplash.com')) {
    // Extract base URL without existing parameters
    const baseUrl = url.split('?')[0];
    return `${baseUrl}?w=${width}&h=${height}&auto=format&fit=crop&q=85&fm=webp`;
  }

  return url;
}

// Generate comprehensive structured data for SEO
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Product",
  "name": product.name,
  "description": product.description,
  "image": product.images,
  "category": product.category,
  "brand": {
    "@type": "Brand",
    "name": "Cheers Marketplace"
  },
  "sku": product.id || generateSlug(product.name),
  "mpn": product.id || generateSlug(product.name),
  "offers": {
    "@type": "Offer",
    "price": product.price,
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock",
    "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    "seller": {
      "@type": "Organization",
      "name": "Cheers Marketplace",
      "url": "https://www.cheersmarketplace.com"
    }
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.5",
    "reviewCount": "1"
  }
};

// Generate breadcrumb structured data
const breadcrumbData = {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://www.cheersmarketplace.com/"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Products",
      "item": "https://www.cheersmarketplace.com/products"
    },
    {
      "@type": "ListItem",
      "position": 3,
      "name": product.name,
      "item": `https://www.cheersmarketplace.com/products/${generateSlug(product.name)}`
    }
  ]
};
---

<Layout
  title={pageTitle}
  description={metaDescription}
>
  <Fragment slot="head">
    <!-- Preload critical LCP image for performance (single preload) -->
    {getFeaturedImage(product) && (
      <link
        rel="preload"
        as="image"
        href={optimizeImageUrl(getFeaturedImage(product) || '/images/product-placeholder.svg', 800, 600)}
        fetchpriority="high"
      />
    )}

    <!-- Product Structured Data -->
    <ProductSchema product={{
      id: product.id || productSlug,
      name: product.name,
      description: product.description,
      price: product.price,
      category: product.category,
      firstImage: getFeaturedImage(product),
      images: product.images,
      slug: productSlug,
      brand: 'Cheers Marketplace',
      condition: 'UsedCondition',
      availability: 'InStock'
    }} />

    <meta name="keywords" content={`${product.name}, ${product.category}, marketplace, ${product.keyPoints?.map(kp => kp.value).join(', ') || ''}`} />
    <!-- Canonical URL handled by Layout.astro with proper trailing slash -->

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content={`${product.name} - $${Number(product.price).toFixed(2)}`} />
    <meta property="og:description" content={product.description} />
    <meta property="og:image" content={optimizeImageUrl(product.images?.[0], 800, 600)} />
    <meta property="og:image:width" content="800" />
    <meta property="og:image:height" content="600" />
    <meta property="og:type" content="product" />
    <meta property="og:url" content={`https://www.cheersmarketplace.com/products/${generateSlug(product.name)}`} />
    <meta property="product:price:amount" content={product.price.toString()} />
    <meta property="product:price:currency" content="USD" />
    <meta property="product:availability" content="in stock" />
    <meta property="product:category" content={product.category} />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={`${product.name} - $${Number(product.price).toFixed(2)}`} />
    <meta name="twitter:description" content={product.description} />
    <meta name="twitter:image" content={optimizeImageUrl(product.images?.[0], 800, 600)} />

    <!-- Structured Data -->
    <script type="application/ld+json" set:html={JSON.stringify(structuredData)} is:inline></script>
    <script type="application/ld+json" set:html={JSON.stringify(breadcrumbData)} is:inline></script>
  </Fragment>

  <Header />

  <!-- Consistent Breadcrumb Placement -->
  <BreadcrumbWrapper
    productName={product.name}
    productSlug={productSlug}
    productCategory={product.category}
  />

  <main class="product-main">

    <!-- Professional Product Layout -->
    <section class="product-section">
      <div class="container">

        <div class="product-grid">
          <!-- Product Images -->
          <div class="product-images">
            <div class="main-image-container">
              <img
                src={optimizeImageUrl(getFeaturedImage(product) || '/images/product-placeholder.svg', 800, 600)}
                alt={product.name}
                class="main-image"
                id="main-product-image"
                loading="eager"
                fetchpriority="high"
                width="800"
                height="600"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
                decoding="async"
                style="cursor: zoom-in;"
                title="Click to zoom"
              />
              {product.defects && (
                <div class="defects-badge">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                  </svg>
                  Has Issues
                </div>
              )}
              <div class="zoom-hint">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                  <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>
                </svg>
                Click to zoom
              </div>

              <!-- Watermark text -->
              <div class="watermark" aria-hidden="true">
                CheersMarketplace.com
              </div>
            </div>

            {product.images && product.images.length > 1 && (
              <div class="image-thumbnails">
                {product.images.map((image, index) => (
                  <button
                    class={`thumbnail ${index === 0 ? 'active' : ''}`}
                    data-image={image}
                    aria-label={`View image ${index + 1}`}
                  >
                    <img
                      src={optimizeImageUrl(image, 160, 160)}
                      alt={`${product.name} view ${index + 1}`}
                      loading="lazy"
                      decoding="async"
                      width="80"
                      height="80"
                      sizes="80px"
                    />
                    <!-- Watermark text for thumbnails -->
                    <div class="thumbnail-watermark" aria-hidden="true">
                      CheersMarketplace.com
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          <!-- Product Information -->
          <div class="product-info">
            <!-- Product Header Section -->
            <div class="product-header">
              <div class="category-badge">{product.category}</div>
              <h1 class="product-title">{product.name}</h1>

              <!-- Product Meta Information -->
              <div class="product-meta-info">
                {product.condition && (
                  <div class="condition-section">
                    <span class="condition-label">Condition:</span>
                    <span class={`condition-badge condition-${product.condition.toLowerCase()}`}>
                      {product.condition}
                    </span>
                    <a href="/condition-guide/" class="condition-guide-link">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"/>
                      </svg>
                      What does this mean?
                    </a>
                  </div>
                )}
              </div>

              <div class="product-price">
                <span class="price-amount">${Number(product.price).toFixed(2)}</span>
                <span class="price-currency">USD</span>
              </div>
            </div>

            <!-- Product Description Section -->
            <div class="product-section">
              <h2 class="section-title">Description</h2>
              <div class="product-description">
                <p>{product.description}</p>
              </div>
            </div>

            <!-- Important Disclaimers -->
            <div class="product-section disclaimers">
              <h2 class="section-title">Important Information</h2>
              <div class="disclaimer-content">
                <p><strong>Color Accuracy:</strong> While we take great care to ensure accurate product representation in our images, colors may appear slightly different in person due to lighting, camera settings, and display variations.</p>
                {product.category.toLowerCase().includes('clothing') || product.category.toLowerCase().includes('fabric') ? (
                  <p><strong>Pet-Friendly Household:</strong> Please note that we have a cat in our household. Although all clothing and fabric items are thoroughly steam cleaned and washed, those with cat allergies may wish to consider their purchase carefully.</p>
                ) : null}
                <p><strong>Quality Assurance:</strong> As a family-run business in Chico, CA, we personally inspect all gently used items for quality and defects before listing. Any noted defects are clearly described in the product details.</p>
              </div>
            </div>

            <!-- Key Features Section -->
            {product.keyPoints && product.keyPoints.length > 0 && (
              <div class="product-section">
                <h2 class="section-title">Key Features</h2>
                <div class="features-grid">
                  {product.keyPoints.map((feature) => (
                    <div class="feature-card">
                      <div class="feature-label">{feature.label}</div>
                      <div class="feature-value">{feature.value}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <!-- Important Notes Section -->
            {product.defects && (
              <div class="product-section">
                <div class="product-defects">
                  <div class="defects-header">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                    </svg>
                    <h2 class="section-title defects-title">Important Notes</h2>
                  </div>
                  <p class="defects-text">{product.defects}</p>
                </div>
              </div>
            )}

            <!-- Purchase Section -->
            <div class="product-section">
              <div class="product-actions">
                <AddToCartButton
                  productId={product.id}
                  productName={product.name}
                  productPrice={product.price}
                  productImage={product.images?.[0]}
                  productUrl={`/products/${productSlug}`}
                  productDescription={product.description}
                  productCategory={product.category}
                  maxQuantity={1}
                  customFields={[
                    {
                      name: "Key Points",
                      value: JSON.stringify(product.keyPoints || []),
                      type: "hidden"
                    }
                  ]}
                  variant="primary"
                  size="large"
                  fullWidth={true}
                />
                <button class="btn-secondary" onclick="navigator.share ? navigator.share({title: '{product.name}', url: window.location.href}) : navigator.clipboard.writeText(window.location.href)">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.6 20.92,19A2.84,2.84 0 0,0 18,16.08Z"/>
                  </svg>
                  Share
                </button>
              </div>

              <!-- Contact Option -->
              <div class="contact-section">
                <p class="contact-text">Questions about this item?</p>
                <button class="btn-link" onclick="window.open('mailto:<EMAIL>?subject=Inquiry about ' + encodeURIComponent('{product.name}'), '_blank')">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,6.89 21.1,6 20,4Z"/>
                  </svg>
                  Contact Us
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Back to Products -->
    <section class="back-section">
      <div class="container">
        <a href="/products" class="back-link">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
          </svg>
          Back to All Products
        </a>
      </div>
    </section>
  </main>

  <!-- Image Zoom Modal -->
  <div id="image-zoom-modal" class="zoom-modal" aria-hidden="true">
    <div class="zoom-modal-overlay" aria-label="Close zoom view"></div>
    <div class="zoom-modal-content">
      <button class="zoom-close-btn" aria-label="Close zoom view">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
      </button>
      <div class="zoom-image-container">
        <img id="zoom-image" src="" alt="" class="zoom-image" draggable="false" />
      </div>
      {product.images && product.images.length > 1 && (
        <div class="zoom-navigation">
          <button class="zoom-nav-btn zoom-prev" aria-label="Previous image">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </svg>
          </button>
          <button class="zoom-nav-btn zoom-next" aria-label="Next image">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
            </svg>
          </button>
        </div>
      )}
      <div class="zoom-controls">
        <button class="zoom-control-btn" id="zoom-in" aria-label="Zoom in">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>
          </svg>
        </button>
        <button class="zoom-control-btn" id="zoom-out" aria-label="Zoom out">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            <path d="M7 9v1h5V9H7z"/>
          </svg>
        </button>
        <button class="zoom-control-btn" id="zoom-reset" aria-label="Reset zoom">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <Footer />
</Layout>

<!-- Enhanced JavaScript for image gallery and zoom functionality -->
<script is:inline>
  document.addEventListener('DOMContentLoaded', () => {
    const mainImage = document.getElementById('main-product-image');
    const thumbnails = document.querySelectorAll('.thumbnail');
    const zoomModal = document.getElementById('image-zoom-modal');
    const zoomImage = document.getElementById('zoom-image');
    const zoomCloseBtn = document.querySelector('.zoom-close-btn');
    const zoomOverlay = document.querySelector('.zoom-modal-overlay');
    const zoomInBtn = document.getElementById('zoom-in');
    const zoomOutBtn = document.getElementById('zoom-out');
    const zoomResetBtn = document.getElementById('zoom-reset');
    const zoomPrevBtn = document.querySelector('.zoom-prev');
    const zoomNextBtn = document.querySelector('.zoom-next');

    // Product images array for navigation
    const productImages = Array.from(document.querySelectorAll('.thumbnail')).map(thumb => thumb.dataset.image);
    if (mainImage && productImages.length === 0) {
      productImages.push(mainImage.src);
    }
    let currentImageIndex = 0;
    let currentZoom = 1;
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let translateX = 0;
    let translateY = 0;

    // Optimize image URL function
    function optimizeImageUrl(url, width = 800, height = 600) {
      if (!url) return '/images/product-placeholder.svg';

      if (url.includes('unsplash.com')) {
        const baseUrl = url.split('?')[0];
        return `${baseUrl}?w=${width}&h=${height}&auto=format&fit=crop&q=85&fm=webp`;
      }

      return url;
    }

    // Thumbnail click handlers
    thumbnails.forEach((thumbnail, index) => {
      thumbnail.addEventListener('click', () => {
        const newImageSrc = thumbnail.dataset.image;
        if (mainImage && newImageSrc) {
          mainImage.src = optimizeImageUrl(newImageSrc, 800, 600);
          mainImage.alt = thumbnail.querySelector('img').alt;
          currentImageIndex = index;

          // Update active thumbnail
          thumbnails.forEach(t => t.classList.remove('active'));
          thumbnail.classList.add('active');
        }
      });
    });

    // Open zoom modal
    function openZoomModal(imageSrc) {
      const highResImageSrc = optimizeImageUrl(imageSrc, 1600, 1200);
      zoomImage.src = highResImageSrc;
      zoomImage.alt = mainImage.alt;
      zoomModal.style.display = 'flex';
      zoomModal.setAttribute('aria-hidden', 'false');
      document.body.style.overflow = 'hidden';

      // Reset zoom and position
      currentZoom = 1;
      translateX = 0;
      translateY = 0;
      updateZoomTransform();
    }

    // Close zoom modal
    function closeZoomModal() {
      zoomModal.style.display = 'none';
      zoomModal.setAttribute('aria-hidden', 'true');
      document.body.style.overflow = '';
    }

    // Update zoom transform
    function updateZoomTransform() {
      zoomImage.style.transform = `scale(${currentZoom}) translate(${translateX}px, ${translateY}px)`;
    }

    // Main image click to open zoom
    if (mainImage) {
      mainImage.addEventListener('click', () => {
        openZoomModal(mainImage.src);
      });
    }

    // Close modal handlers
    if (zoomCloseBtn) {
      zoomCloseBtn.addEventListener('click', closeZoomModal);
    }
    if (zoomOverlay) {
      zoomOverlay.addEventListener('click', closeZoomModal);
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (zoomModal.style.display === 'flex') {
        switch(e.key) {
          case 'Escape':
            closeZoomModal();
            break;
          case 'ArrowLeft':
            if (productImages.length > 1) navigateImage(-1);
            break;
          case 'ArrowRight':
            if (productImages.length > 1) navigateImage(1);
            break;
          case '+':
          case '=':
            zoomIn();
            break;
          case '-':
            zoomOut();
            break;
          case '0':
            resetZoom();
            break;
        }
      }
    });

    // Zoom controls
    function zoomIn() {
      if (currentZoom < 3) {
        currentZoom = Math.min(currentZoom * 1.2, 3);
        updateZoomTransform();
      }
    }

    function zoomOut() {
      if (currentZoom > 0.5) {
        currentZoom = Math.max(currentZoom / 1.2, 0.5);
        updateZoomTransform();
      }
    }

    function resetZoom() {
      currentZoom = 1;
      translateX = 0;
      translateY = 0;
      updateZoomTransform();
    }

    // Zoom button handlers
    if (zoomInBtn) zoomInBtn.addEventListener('click', zoomIn);
    if (zoomOutBtn) zoomOutBtn.addEventListener('click', zoomOut);
    if (zoomResetBtn) zoomResetBtn.addEventListener('click', resetZoom);

    // Image navigation
    function navigateImage(direction) {
      if (productImages.length <= 1) return;

      currentImageIndex = (currentImageIndex + direction + productImages.length) % productImages.length;
      const newImageSrc = productImages[currentImageIndex];
      const highResImageSrc = optimizeImageUrl(newImageSrc, 1600, 1200);
      zoomImage.src = highResImageSrc;

      // Update main image and thumbnail
      if (mainImage) {
        mainImage.src = optimizeImageUrl(newImageSrc, 800, 600);
      }
      thumbnails.forEach((thumb, index) => {
        thumb.classList.toggle('active', index === currentImageIndex);
      });

      // Reset zoom
      resetZoom();
    }

    // Navigation button handlers
    if (zoomPrevBtn) zoomPrevBtn.addEventListener('click', () => navigateImage(-1));
    if (zoomNextBtn) zoomNextBtn.addEventListener('click', () => navigateImage(1));

    // Prevent default image drag behavior
    if (zoomImage) {
      zoomImage.addEventListener('dragstart', (e) => {
        e.preventDefault();
        return false;
      });

      zoomImage.addEventListener('selectstart', (e) => {
        e.preventDefault();
        return false;
      });
    }

    // Mouse wheel zoom
    if (zoomImage) {
      zoomImage.addEventListener('wheel', (e) => {
        e.preventDefault();
        if (e.deltaY < 0) {
          zoomIn();
        } else {
          zoomOut();
        }
      });

      // Pan functionality
      zoomImage.addEventListener('mousedown', (e) => {
        if (currentZoom > 1) {
          isDragging = true;
          startX = e.clientX - translateX;
          startY = e.clientY - translateY;
          zoomImage.style.cursor = 'grabbing';
        }
      });

      document.addEventListener('mousemove', (e) => {
        if (isDragging && currentZoom > 1) {
          const newTranslateX = e.clientX - startX;
          const newTranslateY = e.clientY - startY;

          // Apply some constraints to prevent dragging too far
          const maxTranslate = 200 * currentZoom;
          translateX = Math.max(-maxTranslate, Math.min(maxTranslate, newTranslateX));
          translateY = Math.max(-maxTranslate, Math.min(maxTranslate, newTranslateY));

          updateZoomTransform();
        }
      });

      document.addEventListener('mouseup', () => {
        isDragging = false;
        zoomImage.style.cursor = currentZoom > 1 ? 'grab' : 'zoom-in';
      });
    }
  });
</script>

<style>
  /* Professional Product Detail Styles */
  .product-main {
    padding-top: 0;
  }



  .product-section {
    padding: 3rem 0;
  }

  .product-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
  }

  .product-images {
    position: sticky;
    top: 2rem;
  }

  .main-image-container {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    background: var(--light-background);
    margin-bottom: 1rem;
  }

  .main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .defects-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(245, 158, 11, 0.95);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  /* Watermark styling for main image */
  .watermark {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.5rem;
    border-radius: 0.375rem;
    opacity: 0.6;
    pointer-events: none;
    z-index: 2;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: 0.025em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
  }

  .image-thumbnails {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
  }

  .thumbnail {
    position: relative;
    aspect-ratio: 1;
    border: 2px solid var(--border);
    border-radius: var(--radius);
    overflow: hidden;
    background: none;
    cursor: pointer;
    transition: border-color 0.2s ease;
  }

  .thumbnail:hover,
  .thumbnail.active {
    border-color: var(--primary);
  }

  .thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Watermark styling for thumbnails */
  .thumbnail-watermark {
    position: absolute;
    bottom: 0.25rem;
    left: 0.25rem;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 0.5rem;
    font-weight: 600;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    opacity: 0.5;
    pointer-events: none;
    z-index: 2;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: 0.025em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
  }

  .product-info {
    padding: 0;
  }

  /* Product Header Styles */
  .product-header {
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 1rem;
  }

  .category-badge {
    display: inline-block;
    background: var(--primary);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    margin-bottom: 1rem;
  }

  .product-title {
    font-family: Georgia, 'Times New Roman', Times, serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }

  .product-price {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
  }

  .price-amount {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--primary);
    letter-spacing: -0.025em;
  }

  .price-currency {
    font-size: 1.125rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  /* Section Styles */
  .product-section {
    margin-bottom: 1rem;
  }

  /* Disclaimers Styles */
  .disclaimers {
    background: var(--border-light);
    padding: 1rem;
    border-radius: var(--radius);
    border: none;
    margin-top: 0.75rem;
  }

  .disclaimers .section-title {
    color: var(--text);
    font-size: 1rem;
    margin: 0 0 0.75rem 0;
    font-weight: 600;
  }

  .disclaimer-content p {
    font-size: 0.85rem;
    line-height: 1.5;
    color: var(--text-secondary);
    margin: 0 0 0.5rem 0;
  }

  .disclaimer-content p:last-child {
    margin-bottom: 0;
  }

  .disclaimer-content strong {
    color: var(--text);
    font-weight: 600;
  }

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text);
    margin: 0 0 0.75rem 0;
    font-family: Georgia, 'Times New Roman', Times, serif;
    letter-spacing: -0.025em;
  }

  .product-description p {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin: 0;
  }

  /* Features Grid */
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .feature-card {
    background: var(--light-background);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: 1.25rem;
    transition: all 0.2s ease;
  }

  .feature-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-sm);
  }

  .feature-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
  }

  .feature-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text);
    line-height: 1.4;
  }

  /* Defects Section */
  .product-defects {
    background: linear-gradient(135deg, #fef3cd 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
  }

  .product-defects::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #f59e0b;
  }

  .defects-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .defects-header svg {
    color: #f59e0b;
    flex-shrink: 0;
  }

  .defects-title {
    color: #92400e;
    margin: 0;
    font-size: 1.125rem;
  }

  .defects-text {
    margin: 0;
    color: #92400e;
    line-height: 1.6;
    font-size: 1rem;
  }

  /* Action Buttons */
  .product-actions {
    display: flex;
    gap: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-light);
  }

  .contact-section {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
    text-align: center;
  }

  .contact-text {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .btn-link {
    background: none;
    border: none;
    color: var(--primary);
    cursor: pointer;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    text-decoration: underline;
    transition: color 0.2s ease;
  }

  .btn-link:hover {
    color: var(--primary-dark);
  }

  /* Enhanced button states for cart functionality */
  .btn-primary.btn-loading {
    background: #6b7280 !important;
    cursor: not-allowed !important;
  }

  .btn-primary.btn-added {
    background: #10b981 !important;
    cursor: not-allowed !important;
  }

  .btn-primary.btn-available {
    background: var(--primary) !important;
    cursor: pointer !important;
  }

  .btn-primary.btn-available:hover {
    background: var(--primary-dark) !important;
  }

  .btn-primary:disabled {
    opacity: 0.8;
    transform: none !important;
  }

  .btn-primary:disabled:hover {
    transform: none !important;
  }

  .btn-large {
    padding: 1.25rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    letter-spacing: -0.01em;
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    flex: 1;
    justify-content: center;
    box-shadow: var(--shadow);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .btn-secondary {
    background: var(--light-background);
    color: var(--text-secondary);
    border: 1px solid var(--border);
    padding: 1.25rem 1.5rem;
  }

  .btn-secondary:hover {
    background: var(--border-light);
    color: var(--text);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  .back-section {
    padding: 1rem 0 2rem 0;
    border-top: 1px solid var(--border-light);
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
  }

  .back-link:hover {
    color: var(--primary-dark);
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .product-section {
      padding: 2rem 0;
    }

    .product-grid {
      grid-template-columns: 1fr;
      gap: 3rem;
    }

    .product-images {
      position: static;
    }

    .product-title {
      font-size: 2.25rem;
    }

    .price-amount {
      font-size: 2rem;
    }

    .features-grid {
      grid-template-columns: 1fr;
      gap: 1.25rem;
    }

    .feature-card {
      padding: 1.5rem;
    }

    .product-actions {
      flex-direction: column;
      gap: 1rem;
    }

    .btn-large {
      justify-content: center;
      padding: 1.25rem 2rem;
    }

    .btn-secondary {
      order: -1;
      align-self: flex-start;
      width: auto;
    }

    /* Responsive watermark adjustments */
    .watermark {
      bottom: 0.75rem;
      left: 0.75rem;
      font-size: 0.625rem;
      padding: 0.25rem 0.375rem;
    }

    .thumbnail-watermark {
      bottom: 0.1875rem;
      left: 0.1875rem;
      font-size: 0.4375rem;
      padding: 0.0625rem 0.125rem;
    }
  }

  @media (max-width: 480px) {
    .product-section {
      margin-bottom: 0.75rem;
    }

    .product-header {
      padding-bottom: 0.75rem;
      margin-bottom: 0.75rem;
    }

    .product-title {
      font-size: 2rem;
    }

    .price-amount {
      font-size: 1.75rem;
    }

    .product-description p {
      font-size: 1.125rem;
      line-height: 1.6;
    }

    .section-title {
      font-size: 1.25rem;
    }

    .feature-card {
      padding: 1.25rem;
    }

    .btn-large {
      padding: 1.125rem 2rem;
      font-size: 1rem;
    }

    .product-defects {
      padding: 1.5rem;
    }

    /* Smaller watermarks for mobile */
    .watermark {
      bottom: 0.5rem;
      left: 0.5rem;
      font-size: 0.5rem;
      padding: 0.1875rem 0.25rem;
    }

    .thumbnail-watermark {
      bottom: 0.125rem;
      left: 0.125rem;
      font-size: 0.375rem;
      padding: 0.0625rem 0.125rem;
    }
  }

  /* Condition Badge Styles - Accessibility Compliant */
  .product-meta-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .condition-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .condition-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
  }

  .condition-badge {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border: 1px solid;
  }

  .condition-poor {
    background: #fef2f2;
    color: #991b1b;
    border-color: #fca5a5;
  }

  .condition-fair {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
  }

  .condition-good {
    background: #f0fdf4;
    color: #166534;
    border-color: #86efac;
  }

  .condition-excellent {
    background: #eff6ff;
    color: #1e40af;
    border-color: #93c5fd;
  }

  .condition-new {
    background: #faf5ff;
    color: #7c2d12;
    border-color: #c4b5fd;
  }

  .condition-guide-link {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8125rem;
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
  }

  .condition-guide-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
  }

  @media (max-width: 768px) {
    .condition-section {
      gap: 0.5rem;
    }

    .condition-badge {
      font-size: 0.8125rem;
      padding: 0.25rem 0.5rem;
    }

    .condition-guide-link {
      font-size: 0.75rem;
    }
  }

  /* Zoom hint overlay */
  .zoom-hint {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .main-image-container:hover .zoom-hint {
    opacity: 1;
  }

  /* Zoom Modal Styles */
  .zoom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
  }

  .zoom-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }

  .zoom-modal-content {
    position: relative;
    max-width: 95vw;
    max-height: 95vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .zoom-close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10001;
    transition: all 0.2s ease;
    color: var(--text);
  }

  .zoom-close-btn:hover {
    background: white;
    transform: scale(1.1);
  }

  .zoom-image-container {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    border-radius: var(--radius-lg);
  }

  .zoom-image {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    cursor: zoom-in;
    transition: transform 0.3s ease;
    transform-origin: center;
    user-select: none;
    -webkit-user-drag: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  .zoom-image[style*="scale"] {
    cursor: grab;
  }

  .zoom-image:active {
    cursor: grabbing !important;
  }

  /* Zoom Navigation */
  .zoom-navigation {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 10001;
  }

  .zoom-nav-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    pointer-events: auto;
    color: var(--text);
    margin: 0 1rem;
  }

  .zoom-nav-btn:hover {
    background: white;
    transform: scale(1.1);
  }

  .zoom-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Zoom Controls */
  .zoom-controls {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem;
    border-radius: var(--radius-lg);
    z-index: 10001;
  }

  .zoom-control-btn {
    background: transparent;
    border: none;
    border-radius: var(--radius);
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text);
  }

  .zoom-control-btn:hover {
    background: var(--light-background);
    transform: scale(1.1);
  }

  /* Mobile Zoom Styles */
  @media (max-width: 768px) {
    .zoom-hint {
      bottom: 0.5rem;
      right: 0.5rem;
      font-size: 0.625rem;
      padding: 0.375rem 0.5rem;
    }

    .zoom-modal-content {
      max-width: 100vw;
      max-height: 100vh;
    }

    .zoom-image-container {
      max-width: 100vw;
      max-height: 100vh;
      border-radius: 0;
    }

    .zoom-image {
      max-height: 100vh;
    }

    .zoom-close-btn {
      top: 0.5rem;
      right: 0.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }

    .zoom-nav-btn {
      width: 2.5rem;
      height: 2.5rem;
      margin: 0 0.5rem;
    }

    .zoom-controls {
      bottom: 0.5rem;
      padding: 0.375rem;
    }

    .zoom-control-btn {
      width: 2rem;
      height: 2rem;
    }

    .zoom-control-btn svg {
      width: 16px;
      height: 16px;
    }
  }
</style>

<!-- JavaScript for thumbnail functionality and cart interaction -->
<script is:inline>
  document.addEventListener('DOMContentLoaded', function() {
    // Thumbnail image switching functionality
    const mainImage = document.getElementById('main-product-image');
    const thumbnails = document.querySelectorAll('.thumbnail');

    if (mainImage && thumbnails.length > 0) {
      thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
          const newImageSrc = this.dataset.image;
          if (newImageSrc) {
            // Update main image with optimized URL
            const optimizedUrl = optimizeImageUrl(newImageSrc, 800, 600);
            mainImage.src = optimizedUrl;
            mainImage.alt = this.querySelector('img').alt;

            // Update active thumbnail
            thumbnails.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
          }
        });
      });
    }

    // Image optimization function (same as server-side)
    function optimizeImageUrl(url, width = 800, height = 600) {
      if (!url) return '/images/product-placeholder.svg';

      if (url.includes('unsplash.com')) {
        const baseUrl = url.split('?')[0];
        return `${baseUrl}?w=${width}&h=${height}&auto=format&fit=crop&q=85&fm=webp`;
      }

      return url;
    }

    // Cart functionality is handled by SimpleCartFeedback system
    // No additional cart handling needed here
  });
</script>
